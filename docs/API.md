# API 文档

本文档描述了 Cloudflare Workers AI 聊天应用的 API 接口。

## 基础信息

- **基础 URL**: `https://your-worker-name.your-subdomain.workers.dev`
- **内容类型**: `application/json`
- **认证**: 无需认证（公开 API）

## 端点列表

### 1. 健康检查

检查 API 服务状态。

**端点**: `GET /health`

**响应示例**:
```json
{
  "status": "ok",
  "timestamp": "2025-07-07T05:00:00.000Z"
}
```

**状态码**:
- `200`: 服务正常

---

### 2. 聊天对话

发送消息并获取 AI 回复的流式响应。

**端点**: `POST /api/chat`

**请求头**:
```
Content-Type: application/json
```

**请求体**:
```json
{
  "messages": [
    {
      "role": "user",
      "content": "你好，请介绍一下自己"
    }
  ],
  "stream": true
}
```

**请求参数说明**:

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| messages | Array | 是 | 对话消息数组 |
| messages[].role | String | 是 | 消息角色: "user", "assistant", "system" |
| messages[].content | String | 是 | 消息内容 |
| stream | Boolean | 否 | 是否启用流式响应，默认 true |

**响应格式**:

流式响应使用 Server-Sent Events (SSE) 格式：

```
data: {"content": "你好", "done": false}

data: {"content": "！我是", "done": false}

data: {"content": "基于", "done": false}

data: [DONE]
```

**响应数据说明**:

| 字段 | 类型 | 描述 |
|------|------|------|
| content | String | AI 回复的文本片段 |
| done | Boolean | 是否完成响应 |
| error | String | 错误信息（仅在出错时返回） |

**状态码**:
- `200`: 请求成功，返回流式数据
- `400`: 请求参数错误
- `500`: 服务器内部错误

**错误响应示例**:
```json
{
  "error": "无效的消息格式",
  "details": "messages 字段必须是数组"
}
```

---

## 使用示例

### JavaScript Fetch API

```javascript
async function sendMessage(messages) {
  try {
    const response = await fetch('/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: messages,
        stream: true
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    // 处理流式响应
    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n').filter(line => line.trim());

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') {
            console.log('响应完成');
            return;
          }

          try {
            const parsed = JSON.parse(data);
            if (parsed.content) {
              console.log('收到内容:', parsed.content);
            }
            if (parsed.error) {
              console.error('API 错误:', parsed.error);
              return;
            }
          } catch (e) {
            console.warn('解析响应失败:', e);
          }
        }
      }
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}

// 使用示例
sendMessage([
  { role: 'user', content: '你好' }
]);
```

### cURL 示例

```bash
# 健康检查
curl -X GET https://your-worker.workers.dev/health

# 发送聊天消息
curl -X POST https://your-worker.workers.dev/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user", 
        "content": "你好，请介绍一下自己"
      }
    ],
    "stream": true
  }'
```

### Python 示例

```python
import requests
import json

def send_message(messages):
    url = "https://your-worker.workers.dev/api/chat"
    
    payload = {
        "messages": messages,
        "stream": True
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=payload, headers=headers, stream=True)
        response.raise_for_status()
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data = line[6:]
                    if data == '[DONE]':
                        print("响应完成")
                        break
                    
                    try:
                        parsed = json.loads(data)
                        if 'content' in parsed:
                            print(f"收到内容: {parsed['content']}")
                        if 'error' in parsed:
                            print(f"API 错误: {parsed['error']}")
                            break
                    except json.JSONDecodeError:
                        print(f"解析响应失败: {data}")
                        
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")

# 使用示例
send_message([
    {"role": "user", "content": "你好"}
])
```

## 错误处理

### 常见错误码

| 状态码 | 错误类型 | 描述 | 解决方案 |
|--------|----------|------|----------|
| 400 | Bad Request | 请求参数错误 | 检查请求体格式和必需字段 |
| 429 | Too Many Requests | 请求频率过高 | 实现请求限流和重试机制 |
| 500 | Internal Server Error | 服务器内部错误 | 稍后重试，如持续出现请联系支持 |
| 502 | Bad Gateway | 网关错误 | 检查网络连接，稍后重试 |
| 503 | Service Unavailable | 服务不可用 | 服务维护中，稍后重试 |

### 错误响应格式

```json
{
  "error": "错误描述",
  "details": "详细错误信息",
  "code": "ERROR_CODE",
  "timestamp": "2025-07-07T05:00:00.000Z"
}
```

### 重试策略建议

1. **指数退避**: 重试间隔逐渐增加
2. **最大重试次数**: 建议不超过 3 次
3. **可重试错误**: 5xx 状态码和网络错误
4. **不可重试错误**: 4xx 状态码（除 429 外）

```javascript
async function fetchWithRetry(url, options, maxRetries = 3) {
  for (let i = 0; i <= maxRetries; i++) {
    try {
      const response = await fetch(url, options);
      
      if (response.ok) {
        return response;
      }
      
      // 检查是否应该重试
      if (response.status >= 500 || response.status === 429) {
        if (i < maxRetries) {
          const delay = Math.pow(2, i) * 1000; // 指数退避
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
      }
      
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    } catch (error) {
      if (i === maxRetries) {
        throw error;
      }
      
      const delay = Math.pow(2, i) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}
```

## 限制和配额

### 请求限制
- **并发连接**: 每个客户端最多 10 个并发连接
- **请求频率**: 每分钟最多 60 个请求
- **消息长度**: 单条消息最大 4000 字符
- **对话历史**: 最多保留最近 20 条消息

### Cloudflare Workers 限制
- **CPU 时间**: 每个请求最多 30 秒
- **内存使用**: 最大 128MB
- **响应大小**: 最大 100MB

## 安全考虑

1. **输入验证**: 所有输入都经过验证和清理
2. **速率限制**: 防止滥用和 DDoS 攻击
3. **CORS 配置**: 适当的跨域资源共享设置
4. **日志记录**: 记录请求日志用于监控和调试

## 监控和分析

### 可用的指标
- 请求数量和频率
- 响应时间
- 错误率
- AI 模型使用情况

### 日志格式
```json
{
  "timestamp": "2025-07-07T05:00:00.000Z",
  "method": "POST",
  "path": "/api/chat",
  "status": 200,
  "duration": 1234,
  "userAgent": "Mozilla/5.0...",
  "ip": "***********"
}
```
