# 开发指南

本文档提供了详细的开发和调试指南。

## 🛠️ 开发环境设置

### 1. 环境要求

- Node.js 18.0.0 或更高版本
- npm 9.0.0 或更高版本
- Git
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 2. IDE 推荐配置

#### VS Code 扩展
- ES7+ React/Redux/React-Native snippets
- Prettier - Code formatter
- ESLint
- Auto Rename Tag
- Bracket Pair Colorizer
- GitLens

#### 配置文件
```json
// .vscode/settings.json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

## 🏃‍♂️ 本地开发

### 启动开发服务器

```bash
# 方法 1: 同时启动前后端
npm run dev

# 方法 2: 分别启动
# 终端 1 - 启动后端
npm run dev:backend

# 终端 2 - 启动前端  
npm run dev:frontend
```

### 开发服务器地址
- 前端: http://localhost:5173
- 后端: http://localhost:8787

## 🔍 调试技巧

### 前端调试

1. **浏览器开发者工具**
   - Network 标签页查看 API 请求
   - Console 标签页查看日志输出
   - Application 标签页查看本地存储

2. **React 开发者工具**
   安装 React DevTools 浏览器扩展

3. **网络请求调试**
   ```javascript
   // 在 utils/api.js 中添加调试日志
   console.log('发送请求:', url, options)
   console.log('收到响应:', response)
   ```

### 后端调试

1. **Wrangler 日志**
   ```bash
   cd backend
   npx wrangler tail
   ```

2. **本地调试**
   ```bash
   cd backend
   npm run start
   # 访问 http://localhost:8787
   ```

3. **添加调试日志**
   ```typescript
   // 在 src/index.ts 中添加
   console.log('收到请求:', request.method, request.url)
   console.log('请求体:', await request.json())
   ```

## 🧪 测试

### 手动测试清单

#### 基础功能测试
- [ ] 页面正常加载
- [ ] 发送消息功能
- [ ] 接收 AI 回复
- [ ] 流式输出效果
- [ ] 聊天历史保存

#### 错误处理测试
- [ ] 网络断开时的提示
- [ ] API 错误时的重试机制
- [ ] 无效输入的处理
- [ ] 长时间无响应的处理

#### 响应式设计测试
- [ ] 桌面端显示效果
- [ ] 平板端显示效果
- [ ] 手机端显示效果
- [ ] 横屏/竖屏切换

#### 性能测试
- [ ] 页面加载速度
- [ ] 消息发送响应时间
- [ ] 长对话的性能表现
- [ ] 内存使用情况

### 自动化测试（可选）

```bash
# 安装测试依赖
cd frontend
npm install --save-dev @testing-library/react @testing-library/jest-dom vitest

# 运行测试
npm run test
```

## 🎨 样式开发

### CSS 架构

```
src/
├── App.css          # 主样式文件
├── index.css        # 全局样式
└── components/      # 组件样式（如果拆分组件）
```

### 样式规范

1. **命名约定**
   - 使用 kebab-case: `.chat-header`
   - 组件前缀: `.message-content`
   - 状态类: `.is-loading`, `.is-error`

2. **响应式断点**
   ```css
   /* 移动端 */
   @media (max-width: 768px) { }
   
   /* 小屏移动端 */
   @media (max-width: 480px) { }
   ```

3. **颜色变量**
   ```css
   :root {
     --primary-color: #667eea;
     --error-color: #e53e3e;
     --success-color: #38a169;
   }
   ```

## 🔧 配置管理

### 环境变量

#### 前端环境变量
```bash
# .env.local
VITE_API_URL=http://localhost:8787
VITE_DEBUG=true
```

#### 后端环境变量
```bash
# .env
CLOUDFLARE_API_TOKEN=your_token
CLOUDFLARE_ACCOUNT_ID=your_account_id
```

### 配置文件

#### Vite 配置 (frontend/vite.config.js)
```javascript
export default {
  server: {
    port: 5173,
    proxy: {
      '/api': 'http://localhost:8787'
    }
  }
}
```

#### Wrangler 配置 (backend/wrangler.jsonc)
```json
{
  "name": "ai-chat-backend",
  "main": "src/index.ts",
  "compatibility_date": "2025-07-05",
  "ai": {
    "binding": "AI"
  }
}
```

## 📦 构建和部署

### 本地构建测试

```bash
# 构建前端
cd frontend
npm run build
npm run preview

# 构建后端
cd backend
npm run build
```

### 部署前检查

1. **代码质量检查**
   ```bash
   # 检查 TypeScript 类型
   cd backend && npx tsc --noEmit
   
   # 检查 ESLint（如果配置）
   cd frontend && npm run lint
   ```

2. **环境变量检查**
   - 确保所有必要的环境变量已设置
   - 检查生产环境 URL 配置

3. **功能测试**
   - 在本地环境完整测试所有功能
   - 测试错误处理和边界情况

## 🐛 常见问题解决

### 前端问题

1. **CORS 错误**
   ```
   解决方案: 检查后端 CORS 配置，确保允许前端域名
   ```

2. **API 请求失败**
   ```
   解决方案: 检查 VITE_API_URL 配置，确保后端服务正在运行
   ```

3. **样式不生效**
   ```
   解决方案: 检查 CSS 选择器优先级，清除浏览器缓存
   ```

### 后端问题

1. **Workers AI 访问被拒绝**
   ```
   解决方案: 检查 Cloudflare 账户 Workers AI 权限和余额
   ```

2. **部署失败**
   ```
   解决方案: 检查 wrangler.jsonc 配置，确保 API Token 有效
   ```

3. **流式响应中断**
   ```
   解决方案: 检查网络稳定性，添加错误重试机制
   ```

## 📚 学习资源

### 官方文档
- [Cloudflare Workers](https://developers.cloudflare.com/workers/)
- [Workers AI](https://developers.cloudflare.com/workers-ai/)
- [React](https://react.dev/)
- [Vite](https://vitejs.dev/)

### 社区资源
- [Cloudflare Discord](https://discord.cloudflare.com/)
- [React 社区](https://reactjs.org/community/support.html)
- [Stack Overflow](https://stackoverflow.com/)

## 🤝 贡献指南

1. **代码风格**
   - 使用 Prettier 格式化代码
   - 遵循现有的命名约定
   - 添加必要的注释

2. **提交规范**
   ```
   feat: 添加新功能
   fix: 修复 bug
   docs: 更新文档
   style: 代码格式调整
   refactor: 代码重构
   test: 添加测试
   ```

3. **Pull Request**
   - 提供清晰的描述
   - 包含测试用例
   - 更新相关文档
