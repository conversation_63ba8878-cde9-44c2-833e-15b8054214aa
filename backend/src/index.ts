/**
 * Cloudflare Workers AI 流式聊天应用后端
 * 集成 Gemma 3 12B IT 模型，实现流式响应
 */

interface ChatMessage {
	role: 'user' | 'assistant' | 'system';
	content: string;
}

interface ChatRequest {
	messages: ChatMessage[];
	stream?: boolean;
}

// CORS 头部配置
const corsHeaders = {
	'Access-Control-Allow-Origin': '*',
	'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
	'Access-Control-Allow-Headers': 'Content-Type, Authorization',
	'Access-Control-Max-Age': '86400',
};

export default {
	async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
		// 处理 CORS 预检请求
		if (request.method === 'OPTIONS') {
			return new Response(null, {
				status: 200,
				headers: corsHeaders,
			});
		}

		const url = new URL(request.url);

		// 健康检查端点
		if (url.pathname === '/health') {
			return new Response(JSON.stringify({ status: 'ok', timestamp: new Date().toISOString() }), {
				headers: {
					'Content-Type': 'application/json',
					...corsHeaders
				},
			});
		}

		// 非流式聊天 API 端点
		if (url.pathname === '/api/chat-sync' && request.method === 'POST') {
			try {
				const body: ChatRequest = await request.json();

				if (!body.messages || !Array.isArray(body.messages)) {
					return new Response(JSON.stringify({ error: '无效的消息格式' }), {
						status: 400,
						headers: {
							'Content-Type': 'application/json',
							...corsHeaders
						},
					});
				}

				// 准备发送给 AI 模型的消息
				const messages = body.messages.map(msg => ({
					role: msg.role,
					content: msg.content
				}));

				// 调用 Cloudflare Workers AI (非流式)
				const response = await env.AI.run('@cf/google/gemma-3-12b-it', {
					messages,
					stream: false, // 关闭流式响应
					max_tokens: 1024, // 增加最大 token 数
					temperature: 0.7, // 稍微增加创造性
				});

				// 返回完整响应
				return new Response(JSON.stringify({
					message: response.response,
					usage: response.usage,
					success: true
				}), {
					headers: {
						'Content-Type': 'application/json',
						...corsHeaders
					},
				});

			} catch (error) {
				console.error('同步聊天 API 错误:', error);
				return new Response(JSON.stringify({
					error: '处理请求时发生错误',
					details: error instanceof Error ? error.message : '未知错误',
					success: false
				}), {
					status: 500,
					headers: {
						'Content-Type': 'application/json',
						...corsHeaders
					},
				});
			}
		}

		// 流式聊天 API 端点
		if (url.pathname === '/api/chat' && request.method === 'POST') {
			try {
				const body: ChatRequest = await request.json();

				if (!body.messages || !Array.isArray(body.messages)) {
					return new Response(JSON.stringify({ error: '无效的消息格式' }), {
						status: 400,
						headers: {
							'Content-Type': 'application/json',
							...corsHeaders
						},
					});
				}

				// 准备发送给 AI 模型的消息
				const messages = body.messages.map(msg => ({
					role: msg.role,
					content: msg.content
				}));

				// 调用 Cloudflare Workers AI
				const response = await env.AI.run('@cf/google/gemma-3-12b-it', {
					messages,
					stream: true,
				});

				// 直接返回 Workers AI 的流式响应，添加 CORS 头部
				return new Response(response, {
					headers: {
						'Content-Type': 'text/event-stream',
						'Cache-Control': 'no-cache',
						'Connection': 'keep-alive',
						...corsHeaders,
					},
				});

			} catch (error) {
				console.error('聊天 API 错误:', error);
				return new Response(JSON.stringify({
					error: '处理请求时发生错误',
					details: error instanceof Error ? error.message : '未知错误'
				}), {
					status: 500,
					headers: {
						'Content-Type': 'application/json',
						...corsHeaders
					},
				});
			}
		}

		// 默认响应
		return new Response(JSON.stringify({
			message: 'Cloudflare Workers AI 聊天后端',
			endpoints: {
				health: '/health',
				'chat-streaming': '/api/chat (POST) - 流式响应',
				'chat-sync': '/api/chat-sync (POST) - 同步响应'
			}
		}), {
			headers: {
				'Content-Type': 'application/json',
				...corsHeaders
			},
		});
	},
} satisfies ExportedHandler<Env>;
