{"version": 3, "sources": ["../../../src/index.ts", "../../../node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-r6egIX/middleware-insertion-facade.js", "../../../node_modules/wrangler/templates/middleware/common.ts", "../bundle-r6egIX/middleware-loader.entry.ts"], "sourceRoot": "/Users/<USER>/workers_ai_start_augment/backend/.wrangler/tmp/dev-4doRgn", "sourcesContent": ["/**\n * Cloudflare Workers AI 流式聊天应用后端\n * 集成 Gemma 3 12B IT 模型，实现流式响应\n */\n\ninterface ChatMessage {\n\trole: 'user' | 'assistant' | 'system';\n\tcontent: string;\n}\n\ninterface ChatRequest {\n\tmessages: ChatMessage[];\n\tstream?: boolean;\n}\n\n// CORS 头部配置\nconst corsHeaders = {\n\t'Access-Control-Allow-Origin': '*',\n\t'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n\t'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n\t'Access-Control-Max-Age': '86400',\n};\n\nexport default {\n\tasync fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {\n\t\t// 处理 CORS 预检请求\n\t\tif (request.method === 'OPTIONS') {\n\t\t\treturn new Response(null, {\n\t\t\t\tstatus: 200,\n\t\t\t\theaders: corsHeaders,\n\t\t\t});\n\t\t}\n\n\t\tconst url = new URL(request.url);\n\n\t\t// 健康检查端点\n\t\tif (url.pathname === '/health') {\n\t\t\treturn new Response(JSON.stringify({ status: 'ok', timestamp: new Date().toISOString() }), {\n\t\t\t\theaders: {\n\t\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t\t...corsHeaders\n\t\t\t\t},\n\t\t\t});\n\t\t}\n\n\t\t// 聊天 API 端点\n\t\tif (url.pathname === '/api/chat' && request.method === 'POST') {\n\t\t\ttry {\n\t\t\t\tconst body: ChatRequest = await request.json();\n\n\t\t\t\tif (!body.messages || !Array.isArray(body.messages)) {\n\t\t\t\t\treturn new Response(JSON.stringify({ error: '无效的消息格式' }), {\n\t\t\t\t\t\tstatus: 400,\n\t\t\t\t\t\theaders: {\n\t\t\t\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t\t\t\t...corsHeaders\n\t\t\t\t\t\t},\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\t// 准备发送给 AI 模型的消息\n\t\t\t\tconst messages = body.messages.map(msg => ({\n\t\t\t\t\trole: msg.role,\n\t\t\t\t\tcontent: msg.content\n\t\t\t\t}));\n\n\t\t\t\t// 调用 Cloudflare Workers AI\n\t\t\t\tconst response = await env.AI.run('@hf/google/gemma-3-12b-it', {\n\t\t\t\t\tmessages,\n\t\t\t\t\tstream: true,\n\t\t\t\t});\n\n\t\t\t\t// 创建流式响应\n\t\t\t\tconst { readable, writable } = new TransformStream();\n\t\t\t\tconst writer = writable.getWriter();\n\t\t\t\tconst encoder = new TextEncoder();\n\n\t\t\t\t// 处理流式响应\n\t\t\t\tctx.waitUntil(\n\t\t\t\t\t(async () => {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst reader = response.getReader();\n\n\t\t\t\t\t\t\twhile (true) {\n\t\t\t\t\t\t\t\tconst { done, value } = await reader.read();\n\n\t\t\t\t\t\t\t\tif (done) {\n\t\t\t\t\t\t\t\t\tawait writer.write(encoder.encode('data: [DONE]\\n\\n'));\n\t\t\t\t\t\t\t\t\tawait writer.close();\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t// 解析 AI 响应\n\t\t\t\t\t\t\t\tconst chunk = new TextDecoder().decode(value);\n\t\t\t\t\t\t\t\tconst lines = chunk.split('\\n').filter(line => line.trim());\n\n\t\t\t\t\t\t\t\tfor (const line of lines) {\n\t\t\t\t\t\t\t\t\tif (line.startsWith('data: ')) {\n\t\t\t\t\t\t\t\t\t\tconst data = line.slice(6);\n\t\t\t\t\t\t\t\t\t\tif (data === '[DONE]') {\n\t\t\t\t\t\t\t\t\t\t\tawait writer.write(encoder.encode('data: [DONE]\\n\\n'));\n\t\t\t\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t\t\tconst parsed = JSON.parse(data);\n\t\t\t\t\t\t\t\t\t\t\tif (parsed.response) {\n\t\t\t\t\t\t\t\t\t\t\t\t// 发送流式数据到前端\n\t\t\t\t\t\t\t\t\t\t\t\tawait writer.write(encoder.encode(`data: ${JSON.stringify({\n\t\t\t\t\t\t\t\t\t\t\t\t\tcontent: parsed.response,\n\t\t\t\t\t\t\t\t\t\t\t\t\tdone: false\n\t\t\t\t\t\t\t\t\t\t\t\t})}\\n\\n`));\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\t\t\t\tconsole.error('解析 AI 响应错误:', e);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tconsole.error('流式处理错误:', error);\n\t\t\t\t\t\t\tawait writer.write(encoder.encode(`data: ${JSON.stringify({\n\t\t\t\t\t\t\t\terror: '处理请求时发生错误',\n\t\t\t\t\t\t\t\tdone: true\n\t\t\t\t\t\t\t})}\\n\\n`));\n\t\t\t\t\t\t\tawait writer.close();\n\t\t\t\t\t\t}\n\t\t\t\t\t})()\n\t\t\t\t);\n\n\t\t\t\treturn new Response(readable, {\n\t\t\t\t\theaders: {\n\t\t\t\t\t\t'Content-Type': 'text/event-stream',\n\t\t\t\t\t\t'Cache-Control': 'no-cache',\n\t\t\t\t\t\t'Connection': 'keep-alive',\n\t\t\t\t\t\t...corsHeaders,\n\t\t\t\t\t},\n\t\t\t\t});\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('聊天 API 错误:', error);\n\t\t\t\treturn new Response(JSON.stringify({\n\t\t\t\t\terror: '处理请求时发生错误',\n\t\t\t\t\tdetails: error instanceof Error ? error.message : '未知错误'\n\t\t\t\t}), {\n\t\t\t\t\tstatus: 500,\n\t\t\t\t\theaders: {\n\t\t\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t\t\t...corsHeaders\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\t// 默认响应\n\t\treturn new Response(JSON.stringify({\n\t\t\tmessage: 'Cloudflare Workers AI 聊天后端',\n\t\t\tendpoints: {\n\t\t\t\thealth: '/health',\n\t\t\t\tchat: '/api/chat (POST)'\n\t\t\t}\n\t\t}), {\n\t\t\theaders: {\n\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t...corsHeaders\n\t\t\t},\n\t\t});\n\t},\n} satisfies ExportedHandler<Env>;\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTH<PERSON>_EXPORTS from \"/Users/<USER>/workers_ai_start_augment/backend/src/index.ts\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"/Users/<USER>/workers_ai_start_augment/backend/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"/Users/<USER>/workers_ai_start_augment/backend/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"/Users/<USER>/workers_ai_start_augment/backend/src/index.ts\";\n\t\t\t\tconst MIDDLEWARE_TEST_INJECT = \"__INJECT_FOR_TESTING_WRANGLER_MIDDLEWARE__\";\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"/Users/<USER>/workers_ai_start_augment/backend/.wrangler/tmp/bundle-r6egIX/middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"/Users/<USER>/workers_ai_start_augment/backend/node_modules/wrangler/templates/middleware/common.ts\";\nimport type { WorkerEntrypointConstructor } from \"/Users/<USER>/workers_ai_start_augment/backend/.wrangler/tmp/bundle-r6egIX/middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"/Users/<USER>/workers_ai_start_augment/backend/.wrangler/tmp/bundle-r6egIX/middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;AAgBA,IAAM,cAAc;AAAA,EACnB,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,gCAAgC;AAAA,EAChC,0BAA0B;AAC3B;AAEA,IAAO,cAAQ;AAAA,EACd,MAAM,MAAM,SAAkB,KAAU,KAA0C;AAEjF,QAAI,QAAQ,WAAW,WAAW;AACjC,aAAO,IAAI,SAAS,MAAM;AAAA,QACzB,QAAQ;AAAA,QACR,SAAS;AAAA,MACV,CAAC;AAAA,IACF;AAEA,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAG/B,QAAI,IAAI,aAAa,WAAW;AAC/B,aAAO,IAAI,SAAS,KAAK,UAAU,EAAE,QAAQ,MAAM,YAAW,oBAAI,KAAK,GAAE,YAAY,EAAE,CAAC,GAAG;AAAA,QAC1F,SAAS;AAAA,UACR,gBAAgB;AAAA,UAChB,GAAG;AAAA,QACJ;AAAA,MACD,CAAC;AAAA,IACF;AAGA,QAAI,IAAI,aAAa,eAAe,QAAQ,WAAW,QAAQ;AAC9D,UAAI;AACH,cAAM,OAAoB,MAAM,QAAQ,KAAK;AAE7C,YAAI,CAAC,KAAK,YAAY,CAAC,MAAM,QAAQ,KAAK,QAAQ,GAAG;AACpD,iBAAO,IAAI,SAAS,KAAK,UAAU,EAAE,OAAO,6CAAU,CAAC,GAAG;AAAA,YACzD,QAAQ;AAAA,YACR,SAAS;AAAA,cACR,gBAAgB;AAAA,cAChB,GAAG;AAAA,YACJ;AAAA,UACD,CAAC;AAAA,QACF;AAGA,cAAM,WAAW,KAAK,SAAS,IAAI,UAAQ;AAAA,UAC1C,MAAM,IAAI;AAAA,UACV,SAAS,IAAI;AAAA,QACd,EAAE;AAGF,cAAM,WAAW,MAAM,IAAI,GAAG,IAAI,6BAA6B;AAAA,UAC9D;AAAA,UACA,QAAQ;AAAA,QACT,CAAC;AAGD,cAAM,EAAE,UAAU,SAAS,IAAI,IAAI,gBAAgB;AACnD,cAAM,SAAS,SAAS,UAAU;AAClC,cAAM,UAAU,IAAI,YAAY;AAGhC,YAAI;AAAA,WACF,YAAY;AACZ,gBAAI;AACH,oBAAM,SAAS,SAAS,UAAU;AAElC,qBAAO,MAAM;AACZ,sBAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAE1C,oBAAI,MAAM;AACT,wBAAM,OAAO,MAAM,QAAQ,OAAO,kBAAkB,CAAC;AACrD,wBAAM,OAAO,MAAM;AACnB;AAAA,gBACD;AAGA,sBAAM,QAAQ,IAAI,YAAY,EAAE,OAAO,KAAK;AAC5C,sBAAM,QAAQ,MAAM,MAAM,IAAI,EAAE,OAAO,UAAQ,KAAK,KAAK,CAAC;AAE1D,2BAAW,QAAQ,OAAO;AACzB,sBAAI,KAAK,WAAW,QAAQ,GAAG;AAC9B,0BAAM,OAAO,KAAK,MAAM,CAAC;AACzB,wBAAI,SAAS,UAAU;AACtB,4BAAM,OAAO,MAAM,QAAQ,OAAO,kBAAkB,CAAC;AACrD;AAAA,oBACD;AAEA,wBAAI;AACH,4BAAM,SAAS,KAAK,MAAM,IAAI;AAC9B,0BAAI,OAAO,UAAU;AAEpB,8BAAM,OAAO,MAAM,QAAQ,OAAO,SAAS,KAAK,UAAU;AAAA,0BACzD,SAAS,OAAO;AAAA,0BAChB,MAAM;AAAA,wBACP,CAAC,CAAC;AAAA;AAAA,CAAM,CAAC;AAAA,sBACV;AAAA,oBACD,SAAS,GAAG;AACX,8BAAQ,MAAM,6CAAe,CAAC;AAAA,oBAC/B;AAAA,kBACD;AAAA,gBACD;AAAA,cACD;AAAA,YACD,SAAS,OAAO;AACf,sBAAQ,MAAM,yCAAW,KAAK;AAC9B,oBAAM,OAAO,MAAM,QAAQ,OAAO,SAAS,KAAK,UAAU;AAAA,gBACzD,OAAO;AAAA,gBACP,MAAM;AAAA,cACP,CAAC,CAAC;AAAA;AAAA,CAAM,CAAC;AACT,oBAAM,OAAO,MAAM;AAAA,YACpB;AAAA,UACD,GAAG;AAAA,QACJ;AAEA,eAAO,IAAI,SAAS,UAAU;AAAA,UAC7B,SAAS;AAAA,YACR,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,cAAc;AAAA,YACd,GAAG;AAAA,UACJ;AAAA,QACD,CAAC;AAAA,MAEF,SAAS,OAAO;AACf,gBAAQ,MAAM,kCAAc,KAAK;AACjC,eAAO,IAAI,SAAS,KAAK,UAAU;AAAA,UAClC,OAAO;AAAA,UACP,SAAS,iBAAiB,QAAQ,MAAM,UAAU;AAAA,QACnD,CAAC,GAAG;AAAA,UACH,QAAQ;AAAA,UACR,SAAS;AAAA,YACR,gBAAgB;AAAA,YAChB,GAAG;AAAA,UACJ;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD;AAGA,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MAClC,SAAS;AAAA,MACT,WAAW;AAAA,QACV,QAAQ;AAAA,QACR,MAAM;AAAA,MACP;AAAA,IACD,CAAC,GAAG;AAAA,MACH,SAAS;AAAA,QACR,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACJ;AAAA,IACD,CAAC;AAAA,EACF;AACD;;;ACtKA,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAG;AACX,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAQ;AAChB,UAAM,QAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAM,gCAA8D;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EArBD,OAYoE;AAAA;AAAA;AAAA,EAC1D;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,kCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAEA,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,wBACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B,GAXyE;AAAA,IAazE,cAA0B,wBAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD,GAT0B;AAAA,IAW1B,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": []}