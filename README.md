# Cloudflare Workers AI 流式聊天应用

基于 Cloudflare Workers AI 和 Gemma 3 12B IT 模型构建的现代化流式聊天应用。

## 🌟 功能特性

- **流式响应**: 实时显示 AI 回复，提供流畅的对话体验
- **现代化界面**: 响应式设计，支持移动端和桌面端
- **智能重试**: 自动重试机制，处理网络异常
- **聊天历史**: 本地存储聊天记录
- **错误处理**: 完善的错误提示和处理机制
- **离线检测**: 网络状态监控和提示
- **无服务器**: 基于 Cloudflare Workers，无需管理服务器

## 🏗️ 项目结构

```
├── frontend/          # React 前端应用
│   ├── src/
│   │   ├── App.jsx    # 主应用组件
│   │   ├── App.css    # 样式文件
│   │   └── utils/
│   │       └── api.js # API 工具函数
│   └── package.json
├── backend/           # Cloudflare Workers 后端
│   ├── src/
│   │   └── index.ts   # Workers 脚本
│   ├── wrangler.jsonc # Workers 配置
│   └── package.json
├── docs/              # 文档目录
└── package.json       # 根项目配置
```

## 🚀 快速开始

### 前置要求

- Node.js 18+ 
- npm 或 yarn
- Cloudflare 账户
- Cloudflare Workers AI 访问权限

### 1. 克隆项目

```bash
git clone <repository-url>
cd cloudflare-ai-chat-app
```

### 2. 安装依赖

```bash
# 安装所有依赖
npm run install:all

# 或者分别安装
npm install
cd frontend && npm install
cd ../backend && npm install
```

### 3. 配置环境变量

#### 前端配置
```bash
cd frontend
cp .env.example .env.local
# 编辑 .env.local 文件，设置 API URL
```

#### 后端配置
```bash
cd backend
cp .env.example .env
# 编辑 .env 文件，设置 Cloudflare 凭据
```

### 4. 本地开发

```bash
# 启动后端开发服务器
npm run dev:backend

# 在新终端启动前端开发服务器
npm run dev:frontend

# 或者同时启动前后端
npm run dev
```

访问 http://localhost:5173 查看应用。

## 📦 部署指南

### 部署后端到 Cloudflare Workers

1. **登录 Cloudflare**
```bash
cd backend
npx wrangler login
```

2. **配置 Workers AI**
确保您的 Cloudflare 账户已启用 Workers AI 功能。

3. **部署 Workers**
```bash
npm run deploy
```

4. **记录 Workers URL**
部署成功后，记录返回的 Workers URL，格式类似：
`https://ai-chat-backend.your-subdomain.workers.dev`

### 部署前端

#### 选项 1: Cloudflare Pages

1. **构建前端**
```bash
cd frontend
# 更新 .env.local 中的 VITE_API_URL 为 Workers URL
npm run build
```

2. **部署到 Pages**
- 登录 Cloudflare Dashboard
- 进入 Pages 部分
- 连接 Git 仓库或上传 `dist` 文件夹
- 设置构建命令: `npm run build`
- 设置输出目录: `dist`

#### 选项 2: 其他平台

前端是静态应用，可以部署到任何静态托管平台：
- Vercel
- Netlify  
- GitHub Pages
- 等等

## 🔧 配置说明

### Cloudflare Workers 配置

`backend/wrangler.jsonc` 文件包含 Workers 配置：

```json
{
  "name": "ai-chat-backend",
  "main": "src/index.ts", 
  "compatibility_date": "2025-07-05",
  "ai": {
    "binding": "AI"
  }
}
```

### 环境变量

#### 前端环境变量
- `VITE_API_URL`: Workers API 地址

#### 后端环境变量
- `CLOUDFLARE_API_TOKEN`: Cloudflare API 令牌
- `CLOUDFLARE_ACCOUNT_ID`: Cloudflare 账户 ID

## 🛠️ 开发指南

### 添加新功能

1. **后端 API 扩展**
编辑 `backend/src/index.ts` 添加新的端点。

2. **前端功能扩展**  
编辑 `frontend/src/App.jsx` 添加新的 UI 组件。

3. **样式修改**
编辑 `frontend/src/App.css` 修改样式。

### 调试技巧

1. **查看 Workers 日志**
```bash
cd backend
npx wrangler tail
```

2. **本地调试**
```bash
cd backend  
npm run start
```

3. **前端调试**
使用浏览器开发者工具查看网络请求和控制台输出。

## 📝 API 文档

### POST /api/chat

发送聊天消息并获取流式响应。

**请求体:**
```json
{
  "messages": [
    {
      "role": "user",
      "content": "你好"
    }
  ],
  "stream": true
}
```

**响应:**
Server-Sent Events 格式的流式数据。

### GET /health

健康检查端点。

**响应:**
```json
{
  "status": "ok",
  "timestamp": "2025-07-07T05:00:00.000Z"
}
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 故障排除

### 常见问题

1. **Workers AI 访问被拒绝**
   - 确保 Cloudflare 账户已启用 Workers AI
   - 检查账户余额和使用限制

2. **CORS 错误**
   - 确保后端正确设置了 CORS 头部
   - 检查前端 API URL 配置

3. **部署失败**
   - 检查 Cloudflare API 令牌权限
   - 确保账户 ID 正确

4. **流式响应中断**
   - 检查网络连接稳定性
   - 查看 Workers 日志排查错误

### 获取帮助

- 查看 [Cloudflare Workers 文档](https://developers.cloudflare.com/workers/)
- 查看 [Workers AI 文档](https://developers.cloudflare.com/workers-ai/)
- 提交 Issue 到项目仓库
