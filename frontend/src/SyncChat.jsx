import { useState, useRef, useEffect } from 'react'
import { Send, Bot, User, Loader2, Refresh<PERSON>w, Trash2, Clock, Zap } from 'lucide-react'
import { saveChatHistory, loadChatHistory, clearChatHistory } from './utils/api'
import './SyncChat.css'

function SyncChat() {
  const [messages, setMessages] = useState([])
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const [responseTime, setResponseTime] = useState(null)
  const messagesEndRef = useRef(null)
  const inputRef = useRef(null)

  // 初始化：加载聊天历史
  useEffect(() => {
    const history = loadChatHistory()
    if (history.length > 0) {
      setMessages(history)
    }
  }, [])

  // 保存聊天历史
  useEffect(() => {
    if (messages.length > 0) {
      saveChatHistory(messages)
    }
  }, [messages])

  // 监听网络状态
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 发送消息 (同步方式)
  const sendMessage = async () => {
    if (!input.trim() || isLoading || !isOnline) return

    const userMessage = { role: 'user', content: input.trim() }
    const newMessages = [...messages, userMessage]
    setMessages(newMessages)
    setInput('')
    setIsLoading(true)
    setError(null)
    setResponseTime(null)

    const startTime = Date.now()

    try {
      // 调用同步 API
      const response = await fetch('http://localhost:8787/api/chat-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: newMessages,
          stream: false,
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      const endTime = Date.now()
      const duration = endTime - startTime

      setResponseTime(duration)

      if (data.success && data.message) {
        // 添加 AI 回复
        const assistantMessage = { 
          role: 'assistant', 
          content: data.message,
          usage: data.usage,
          responseTime: duration
        }
        setMessages(prev => [...prev, assistantMessage])
      } else {
        throw new Error(data.error || '未知错误')
      }
    } catch (error) {
      console.error('发送消息错误:', error)
      setError('发送消息失败，请检查网络连接或稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 重试发送消息
  const retryMessage = async () => {
    if (isLoading) return
    
    // 重新发送最后一条用户消息
    const lastUserMessage = messages.findLast(msg => msg.role === 'user')
    if (lastUserMessage) {
      setInput(lastUserMessage.content)
      setTimeout(() => sendMessage(), 100)
    }
  }

  // 清除聊天历史
  const clearMessages = () => {
    setMessages([])
    clearChatHistory()
    setError(null)
    setResponseTime(null)
  }

  // 清除错误
  const clearError = () => {
    setError(null)
  }

  // 处理键盘事件
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  return (
    <div className="sync-chat-app">
      <header className="sync-chat-header">
        <div className="header-content">
          <Zap className="header-icon" />
          <div className="header-text">
            <h1>AI 聊天助手 (同步模式)</h1>
            <span className="subtitle">基于 Cloudflare Workers AI & Gemma 3 12B IT - 完整响应模式</span>
          </div>
          <div className="header-actions">
            {!isOnline && (
              <div className="offline-indicator">
                <span>离线</span>
              </div>
            )}
            {responseTime && (
              <div className="response-time">
                <Clock size={16} />
                <span>{responseTime}ms</span>
              </div>
            )}
            {messages.length > 0 && (
              <button onClick={clearMessages} className="clear-button" title="清除聊天历史">
                <Trash2 size={18} />
              </button>
            )}
          </div>
        </div>
      </header>

      <main className="sync-chat-main">
        <div className="messages-container">
          {messages.length === 0 && (
            <div className="welcome-message">
              <Zap size={48} className="welcome-icon" />
              <h2>欢迎使用同步模式 AI 聊天助手</h2>
              <p>在同步模式下，AI 会一次性返回完整的回复，而不是流式输出。这种模式适合需要完整响应的场景。</p>
              <div className="mode-features">
                <div className="feature">
                  <Clock size={20} />
                  <span>显示响应时间</span>
                </div>
                <div className="feature">
                  <Bot size={20} />
                  <span>完整回复</span>
                </div>
                <div className="feature">
                  <Zap size={20} />
                  <span>使用统计</span>
                </div>
              </div>
            </div>
          )}

          {messages.map((message, index) => (
            <div key={index} className={`message ${message.role}`}>
              <div className="message-avatar">
                {message.role === 'user' ? <User size={20} /> : <Bot size={20} />}
              </div>
              <div className="message-content">
                <div className="message-text">
                  {message.content}
                </div>
                {message.usage && (
                  <div className="message-stats">
                    <span>输入: {message.usage.prompt_tokens} tokens</span>
                    <span>输出: {message.usage.completion_tokens} tokens</span>
                    {message.responseTime && (
                      <span>响应时间: {message.responseTime}ms</span>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="message assistant">
              <div className="message-avatar">
                <Bot size={20} />
              </div>
              <div className="message-content">
                <div className="loading-indicator">
                  <Loader2 className="spinner" size={16} />
                  <span>AI 正在生成完整回复...</span>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {error && (
          <div className="error-banner">
            <div className="error-content">
              <span>{error}</span>
              <button onClick={retryMessage} className="retry-button">
                <RefreshCw size={16} />
                重试
              </button>
            </div>
            <button onClick={clearError} className="error-close">×</button>
          </div>
        )}

        <div className="input-container">
          <div className="input-wrapper">
            <textarea
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="输入您的消息..."
              className="message-input"
              rows={1}
              disabled={isLoading}
            />
            <button
              onClick={sendMessage}
              disabled={!input.trim() || isLoading || !isOnline}
              className="send-button"
              title={!isOnline ? '网络连接已断开' : '发送消息'}
            >
              <Send size={20} />
            </button>
          </div>
        </div>
      </main>
    </div>
  )
}

export default SyncChat
