/* 聊天应用样式 */
.chat-app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON>o', sans-serif;
}

/* 头部样式 */
.chat-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 0.5rem;
  border-radius: 12px;
}

.chat-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
}

.subtitle {
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
}

.offline-indicator {
  background: #fed7d7;
  color: #c53030;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.clear-button {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: none;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-button:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.05);
}

/* 主要聊天区域 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 欢迎消息 */
.welcome-message {
  text-align: center;
  padding: 3rem 2rem;
  color: white;
  max-width: 600px;
  margin: 0 auto;
}

.welcome-icon {
  background: rgba(255, 255, 255, 0.2);
  padding: 1rem;
  border-radius: 50%;
  margin-bottom: 1.5rem;
}

.welcome-message h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.welcome-message p {
  font-size: 1.125rem;
  opacity: 0.9;
  line-height: 1.6;
}

/* 消息样式 */
.message {
  display: flex;
  gap: 1rem;
  max-width: 80%;
  animation: fadeIn 0.3s ease-out;
}

.message.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message.assistant {
  align-self: flex-start;
}

.message-avatar {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.message.user .message-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.message.assistant .message-avatar {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-text {
  background: rgba(255, 255, 255, 0.95);
  padding: 1rem 1.25rem;
  border-radius: 18px;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #2d3748;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  word-wrap: break-word;
}

.message.user .message-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* 加载指示器 */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.95);
  padding: 1rem 1.25rem;
  border-radius: 18px;
  color: #718096;
  font-size: 0.95rem;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 错误横幅 */
.error-banner {
  background: #fed7d7;
  color: #c53030;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-left: 4px solid #e53e3e;
  margin: 0 2rem;
  border-radius: 8px;
  font-size: 0.9rem;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.retry-button {
  background: #c53030;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: #b91c1c;
  transform: scale(1.05);
}

.error-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.retry-button {
  background: #c53030;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: #b91c1c;
  transform: scale(1.05);
}

.error-close {
  background: none;
  border: none;
  color: #c53030;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.error-close:hover {
  background: rgba(197, 48, 48, 0.1);
}

/* 输入区域 */
.input-container {
  padding: 1.5rem 2rem 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.input-wrapper {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
  max-width: 800px;
  margin: 0 auto;
}

.message-input {
  flex: 1;
  min-height: 44px;
  max-height: 120px;
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 22px;
  font-size: 1rem;
  font-family: inherit;
  resize: none;
  outline: none;
  transition: all 0.2s ease;
  background: white;
  color: #2d3748;
}

.message-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.message-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.message-input::placeholder {
  color: #a0aec0;
}

.send-button {
  width: 44px;
  height: 44px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-header {
    padding: 1rem;
  }

  .header-content {
    gap: 0.75rem;
  }

  .chat-header h1 {
    font-size: 1.25rem;
  }

  .subtitle {
    font-size: 0.8rem;
  }

  .messages-container {
    padding: 1rem;
    gap: 1rem;
  }

  .message {
    max-width: 90%;
  }

  .message-avatar {
    width: 36px;
    height: 36px;
  }

  .message-text {
    padding: 0.875rem 1rem;
    font-size: 0.9rem;
  }

  .input-container {
    padding: 1rem;
  }

  .input-wrapper {
    gap: 0.75rem;
  }

  .welcome-message {
    padding: 2rem 1rem;
  }

  .welcome-message h2 {
    font-size: 1.5rem;
  }

  .welcome-message p {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .chat-header {
    padding: 0.75rem;
  }

  .messages-container {
    padding: 0.75rem;
  }

  .input-container {
    padding: 0.75rem;
  }

  .message {
    max-width: 95%;
  }

  .message-text {
    padding: 0.75rem;
    font-size: 0.875rem;
  }
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
