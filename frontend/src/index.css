/* 全局样式重置和基础设置 */
* {
  box-sizing: border-box;
}

:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #2d3748;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 移除默认的按钮和输入框样式 */
button, input, textarea {
  font-family: inherit;
}

/* 确保在移动设备上的良好体验 */
@media (max-width: 768px) {
  body {
    font-size: 16px; /* 防止 iOS Safari 缩放 */
  }
}
