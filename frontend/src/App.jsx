import { useState } from 'react'
import { Zap, MessageCircle, ToggleLeft, ToggleRight } from 'lucide-react'
import Streaming<PERSON>hat from './StreamingChat'
import SyncChat from './SyncChat'
import './App.css'

function App() {
  const [isStreamingMode, setIsStreamingMode] = useState(true)

  // 初始化：加载聊天历史
  useEffect(() => {
    const history = loadChatHistory()
    if (history.length > 0) {
      setMessages(history)
    }
  }, [])

  // 保存聊天历史
  useEffect(() => {
    if (messages.length > 0) {
      saveChatHistory(messages)
    }
  }, [messages])

  // 监听网络状态
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 发送消息
  const sendMessage = async () => {
    if (!input.trim() || isLoading || !isOnline) return

    const userMessage = { role: 'user', content: input.trim() }
    const newMessages = [...messages, userMessage]
    setMessages(newMessages)
    setInput('')
    setIsLoading(true)
    setError(null)
    setRetryCount(0)

    // 创建助手消息
    const assistantMessage = { role: 'assistant', content: '' }
    setMessages(prev => [...prev, assistantMessage])

    // 处理流式响应
    await sendChatMessage(
      newMessages,
      // onChunk: 处理每个数据块
      (content) => {
        setMessages(prev => {
          const updated = [...prev]
          const lastMessage = updated[updated.length - 1]
          if (lastMessage.role === 'assistant') {
            lastMessage.content += content
          }
          return updated
        })
      },
      // onError: 处理错误
      (errorMessage) => {
        setError(errorMessage)
        setIsLoading(false)
        // 移除未完成的助手消息
        setMessages(prev => prev.filter(msg => !(msg.role === 'assistant' && !msg.content)))
      }
    )

    setIsLoading(false)
  }

  // 处理键盘事件
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  // 重试发送消息
  const retryMessage = async () => {
    if (isLoading) return

    setRetryCount(prev => prev + 1)

    // 移除最后一条失败的助手消息（如果存在）
    const lastMessage = messages[messages.length - 1]
    if (lastMessage && lastMessage.role === 'assistant' && !lastMessage.content) {
      setMessages(prev => prev.slice(0, -1))
    }

    // 重新发送最后一条用户消息
    const lastUserMessage = messages.findLast(msg => msg.role === 'user')
    if (lastUserMessage) {
      setInput(lastUserMessage.content)
      setTimeout(() => sendMessage(), 100)
    }
  }

  // 清除聊天历史
  const clearMessages = () => {
    setMessages([])
    clearChatHistory()
    setError(null)
  }

  // 清除错误
  const clearError = () => {
    setError(null)
  }

  return (
    <div className="chat-app">
      <header className="chat-header">
        <div className="header-content">
          <Bot className="header-icon" />
          <div className="header-text">
            <h1>AI 聊天助手</h1>
            <span className="subtitle">基于 Cloudflare Workers AI & Gemma 3 12B IT</span>
          </div>
          <div className="header-actions">
            {!isOnline && (
              <div className="offline-indicator">
                <span>离线</span>
              </div>
            )}
            {messages.length > 0 && (
              <button onClick={clearMessages} className="clear-button" title="清除聊天历史">
                <Trash2 size={18} />
              </button>
            )}
          </div>
        </div>
      </header>

      <main className="chat-main">
        <div className="messages-container">
          {messages.length === 0 && (
            <div className="welcome-message">
              <Bot size={48} className="welcome-icon" />
              <h2>欢迎使用 AI 聊天助手</h2>
              <p>我是基于 Gemma 3 12B IT 模型的智能助手，可以帮助您解答问题、进行对话。</p>
            </div>
          )}

          {messages.map((message, index) => (
            <div key={index} className={`message ${message.role}`}>
              <div className="message-avatar">
                {message.role === 'user' ? <User size={20} /> : <Bot size={20} />}
              </div>
              <div className="message-content">
                <div className="message-text">
                  {message.content}
                </div>
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="message assistant">
              <div className="message-avatar">
                <Bot size={20} />
              </div>
              <div className="message-content">
                <div className="typing-indicator">
                  <Loader2 className="spinner" size={16} />
                  <span>AI 正在思考...</span>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {error && (
          <div className="error-banner">
            <div className="error-content">
              <span>{error}</span>
              {retryCount < 3 && (
                <button onClick={retryMessage} className="retry-button">
                  <RefreshCw size={16} />
                  重试
                </button>
              )}
            </div>
            <button onClick={clearError} className="error-close">×</button>
          </div>
        )}

        <div className="input-container">
          <div className="input-wrapper">
            <textarea
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="输入您的消息..."
              className="message-input"
              rows={1}
              disabled={isLoading}
            />
            <button
              onClick={sendMessage}
              disabled={!input.trim() || isLoading || !isOnline}
              className="send-button"
              title={!isOnline ? '网络连接已断开' : '发送消息'}
            >
              <Send size={20} />
            </button>
          </div>
        </div>
      </main>
    </div>
  )
}

export default App
