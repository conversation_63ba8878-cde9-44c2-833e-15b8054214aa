// API 工具函数和配置

// API 基础 URL - 在生产环境中应该从环境变量读取
export const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8787'

// 重试配置
const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000, // 1秒
  maxDelay: 10000, // 10秒
}

// 延迟函数
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// 计算重试延迟（指数退避）
const getRetryDelay = (attempt) => {
  const delay = RETRY_CONFIG.baseDelay * Math.pow(2, attempt)
  return Math.min(delay, RETRY_CONFIG.maxDelay)
}

// 检查网络连接
export const checkNetworkConnection = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/health`, {
      method: 'GET',
      timeout: 5000,
    })
    return response.ok
  } catch (error) {
    return false
  }
}

// 带重试的 fetch 函数
export const fetchWithRetry = async (url, options = {}, retryCount = 0) => {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    return response
  } catch (error) {
    // 如果是网络错误且还有重试次数，则重试
    if (retryCount < RETRY_CONFIG.maxRetries && isRetryableError(error)) {
      const delayMs = getRetryDelay(retryCount)
      console.log(`请求失败，${delayMs}ms 后重试 (${retryCount + 1}/${RETRY_CONFIG.maxRetries})`)
      
      await delay(delayMs)
      return fetchWithRetry(url, options, retryCount + 1)
    }

    throw error
  }
}

// 判断是否为可重试的错误
const isRetryableError = (error) => {
  // 网络错误
  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    return true
  }
  
  // 服务器错误 (5xx)
  if (error.message.includes('HTTP 5')) {
    return true
  }
  
  // 超时错误
  if (error.name === 'AbortError') {
    return true
  }
  
  return false
}

// 发送聊天消息的 API 函数
export const sendChatMessage = async (messages, onChunk, onError) => {
  try {
    const response = await fetchWithRetry(`${API_BASE_URL}/api/chat`, {
      method: 'POST',
      body: JSON.stringify({
        messages,
        stream: true,
      }),
    })

    // 处理流式响应
    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let buffer = ''

    while (true) {
      const { done, value } = await reader.read()
      if (done) break

      buffer += decoder.decode(value, { stream: true })
      const lines = buffer.split('\n')
      
      // 保留最后一行（可能不完整）
      buffer = lines.pop() || ''

      for (const line of lines) {
        if (line.trim() && line.startsWith('data: ')) {
          const data = line.slice(6).trim()
          
          if (data === '[DONE]') {
            return
          }

          try {
            const parsed = JSON.parse(data)
            if (parsed.content) {
              onChunk(parsed.content)
            }
            if (parsed.error) {
              onError(parsed.error)
              return
            }
          } catch (e) {
            console.warn('解析 SSE 数据失败:', e, data)
          }
        }
      }
    }
  } catch (error) {
    console.error('发送聊天消息失败:', error)
    onError(getErrorMessage(error))
  }
}

// 获取用户友好的错误消息
export const getErrorMessage = (error) => {
  if (!navigator.onLine) {
    return '网络连接已断开，请检查您的网络设置'
  }

  if (error.message.includes('HTTP 429')) {
    return '请求过于频繁，请稍后再试'
  }

  if (error.message.includes('HTTP 5')) {
    return '服务器暂时不可用，请稍后重试'
  }

  if (error.message.includes('HTTP 4')) {
    return '请求格式错误，请刷新页面重试'
  }

  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    return '无法连接到服务器，请检查网络连接'
  }

  return '发生未知错误，请稍后重试'
}

// 本地存储聊天历史
export const saveChatHistory = (messages) => {
  try {
    localStorage.setItem('chatHistory', JSON.stringify(messages))
  } catch (error) {
    console.warn('保存聊天历史失败:', error)
  }
}

// 加载聊天历史
export const loadChatHistory = () => {
  try {
    const history = localStorage.getItem('chatHistory')
    return history ? JSON.parse(history) : []
  } catch (error) {
    console.warn('加载聊天历史失败:', error)
    return []
  }
}

// 清除聊天历史
export const clearChatHistory = () => {
  try {
    localStorage.removeItem('chatHistory')
  } catch (error) {
    console.warn('清除聊天历史失败:', error)
  }
}
