#!/bin/bash

# Cloudflare Workers AI 聊天应用部署脚本

set -e

echo "🚀 开始部署 Cloudflare Workers AI 聊天应用..."

# 检查必要的工具
check_requirements() {
    echo "📋 检查部署要求..."
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        echo "❌ npm 未安装，请先安装 npm"
        exit 1
    fi
    
    echo "✅ 部署要求检查通过"
}

# 安装依赖
install_dependencies() {
    echo "📦 安装项目依赖..."
    
    # 安装根目录依赖
    npm install
    
    # 安装前端依赖
    cd frontend
    npm install
    cd ..
    
    # 安装后端依赖
    cd backend
    npm install
    cd ..
    
    echo "✅ 依赖安装完成"
}

# 构建前端
build_frontend() {
    echo "🏗️ 构建前端应用..."
    
    cd frontend
    
    # 检查环境变量文件
    if [ ! -f .env.local ]; then
        echo "⚠️  未找到 .env.local 文件，使用默认配置"
        cp .env.example .env.local
    fi
    
    npm run build
    cd ..
    
    echo "✅ 前端构建完成"
}

# 部署后端
deploy_backend() {
    echo "☁️ 部署后端到 Cloudflare Workers..."
    
    cd backend
    
    # 检查是否已登录 Cloudflare
    if ! npx wrangler whoami &> /dev/null; then
        echo "🔐 请先登录 Cloudflare:"
        npx wrangler login
    fi
    
    # 部署 Workers
    npm run deploy
    cd ..
    
    echo "✅ 后端部署完成"
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "🎉 部署完成！"
    echo ""
    echo "📋 部署信息:"
    echo "   - 后端 (Cloudflare Workers): 请查看上方输出的 Workers URL"
    echo "   - 前端构建文件: frontend/dist/"
    echo ""
    echo "📝 下一步:"
    echo "   1. 记录 Workers URL"
    echo "   2. 更新前端 .env.local 中的 VITE_API_URL"
    echo "   3. 重新构建前端: cd frontend && npm run build"
    echo "   4. 将 frontend/dist/ 部署到静态托管平台"
    echo ""
    echo "🔗 推荐的前端托管平台:"
    echo "   - Cloudflare Pages: https://pages.cloudflare.com/"
    echo "   - Vercel: https://vercel.com/"
    echo "   - Netlify: https://netlify.com/"
    echo ""
}

# 主函数
main() {
    check_requirements
    install_dependencies
    build_frontend
    deploy_backend
    show_deployment_info
}

# 运行主函数
main "$@"
